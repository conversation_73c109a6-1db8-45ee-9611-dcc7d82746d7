#pragma once

#include <array>
#include <atomic> // Added for std::atomic
#include <chrono>
#include <cstdint>
#include <functional>
#include <memory>
#include <mutex>
#include <queue>
#include <stdexcept>
#include <string>
#include <unordered_map>
#include <vector>

#include "decoded_instruction.h"
#include "instruction_decoder.h"

// Forward declarations
namespace x86_64 {
class X86_64CPU;
class X86_64JITCompiler;
} // namespace x86_64

namespace x86_64 {

// Definition of OptimizationHints
struct OptimizationHints {
  uint32_t fetch_width = 1;
  uint32_t decode_width = 1;
  uint32_t execute_width = 1;
  bool enable_branch_prediction = true;
  bool enable_out_of_order = false;
  uint32_t max_instructions_per_cycle = 4;
  // Add more fields as needed
};

// Exception class for pipeline errors
struct PipelineException : std::runtime_error {
  explicit PipelineException(const std::string &msg)
      : std::runtime_error(msg) {}
};

// Enhanced two-level adaptive branch predictor
class BranchPredictor {
public:
  static constexpr size_t LOCAL_HISTORY_TABLE_SIZE = 1024;
  static constexpr size_t GLOBAL_HISTORY_BITS = 12;
  static constexpr size_t LOCAL_HISTORY_BITS = 10;
  static constexpr size_t PATTERN_TABLE_SIZE =
      1 << GLOBAL_HISTORY_BITS; // 2^12 for global
  static constexpr size_t LOCAL_PATTERN_TABLE_SIZE =
      1 << LOCAL_HISTORY_BITS; // 2^10 for local
  static constexpr size_t CHOICE_TABLE_SIZE =
      1 << GLOBAL_HISTORY_BITS; // Often same size as global pattern table

  BranchPredictor();

  bool PredictTaken(uint64_t pc);
  void Update(uint64_t pc, bool taken);
  void RecordPrediction(bool correct);
  void ResetStats();

  // Advanced prediction methods
  uint64_t PredictTarget(uint64_t pc, const DecodedInstruction &instr,
                         uint64_t fallthrough);
  void UpdateTarget(uint64_t pc, uint64_t actual_target);
  bool IsIndirectBranch(const DecodedInstruction &instr) const;

  // Statistics
  uint64_t GetHits() const { return m_hits; }
  uint64_t GetMisses() const { return m_misses; }
  double GetAccuracy() const {
    uint64_t total = m_hits + m_misses;
    return total > 0 ? static_cast<double>(m_hits) / total : 0.0;
  }

  // Detailed statistics
  struct PredictorStats {
    uint64_t local_correct;
    uint64_t local_incorrect;
    uint64_t global_correct;
    uint64_t global_incorrect;
    uint64_t choice_local_selected;
    uint64_t choice_global_selected;
    uint64_t target_correct;
    uint64_t target_incorrect;

    PredictorStats()
        : local_correct(0), local_incorrect(0), global_correct(0),
          global_incorrect(0), choice_local_selected(0),
          choice_global_selected(0), target_correct(0), target_incorrect(0) {}
  };

  const PredictorStats &GetDetailedStats() const { return m_detailed_stats; }

private:
  // Two-level adaptive predictor components
  std::vector<uint16_t> m_local_history_table; // Local branch history per PC
  std::vector<uint8_t> m_local_pattern_table;  // Local pattern predictor (2-bit
                                               // saturating counter)
  std::vector<uint8_t> m_global_pattern_table; // Global pattern predictor
                                               // (2-bit saturating counter)
  std::vector<uint8_t> m_choice_table; // Meta-predictor for choosing local vs
                                       // global (2-bit saturating counter)
  uint16_t m_global_history;           // Global history register

  // Branch target buffer
  struct BTBEntry {
    uint64_t tag;
    uint64_t target;
    bool valid;
    uint8_t confidence; // 2-bit confidence counter (0-3)

    BTBEntry() : tag(0), target(0), valid(false), confidence(1) {}
  };
  std::vector<BTBEntry> m_btb;
  static constexpr size_t BTB_SIZE = 512;

  // Return address stack for call/ret prediction
  std::vector<uint64_t> m_ras;
  static constexpr size_t RAS_SIZE = 32;
  size_t m_ras_top; // Pointer to the top of the RAS

  // Statistics
  uint64_t m_hits;
  uint64_t m_misses;
  PredictorStats m_detailed_stats;

  // Helper methods
  size_t GetLocalHistoryIndex(uint64_t pc) const;
  size_t GetGlobalPatternIndex() const;
  size_t GetLocalPatternIndex(uint64_t pc) const;
  size_t GetChoiceIndex(uint64_t pc) const;
  size_t GetBTBIndex(uint64_t pc) const;
  void UpdateCounter(uint8_t &counter, bool taken,
                     uint8_t max_val = 3); // Default max_val for 2-bit counter
};

// Enhanced hazard detection structures
enum class HazardType {
  None,
  RAW, // Read After Write
  WAR, // Write After Read
  WAW, // Write After Write
  Structural
};

struct HazardInfo {
  HazardType type;
  uint64_t blocking_pc;
  uint32_t blocking_stage; // 1: Fetch, 2: Decode, 3: Execute, 4: Memory, 5:
                           // WriteBack
  uint32_t cycles_remaining;
  std::string description;

  HazardInfo()
      : type(HazardType::None), blocking_pc(0), blocking_stage(0),
        cycles_remaining(0) {}
};

// Resource tracking for structural hazards
struct ExecutionUnit {
  enum Type {
    ALU,
    FPU,
    LOAD_STORE,
    BRANCH,
    SIMD,
    DIVIDE,
    UNKNOWN
  }; // Added UNKNOWN for safety

  Type type;
  bool busy;
  uint64_t available_cycle; // Cycle when this unit will be free
  uint64_t instruction_pc;  // PC of the instruction currently using this unit

  ExecutionUnit(Type t = UNKNOWN) // Default constructor
      : type(t), busy(false), available_cycle(0), instruction_pc(0) {}
};

// Pipeline stage structures
struct FetchStage {
  uint64_t pc;
  DecodedInstruction instr;
  bool valid;
  bool predicted_taken;
  uint64_t predicted_target;
  uint64_t fetch_cycle;
  uint8_t prediction_confidence; // 0-3 confidence level

  FetchStage()
      : pc(0), valid(false), predicted_taken(false), predicted_target(0),
        fetch_cycle(0), prediction_confidence(1) {}
};

struct DecodeStage {
  DecodedInstruction instr;
  uint64_t pc;
  bool valid;
  bool predicted_taken;
  uint64_t predicted_target;
  uint64_t decode_cycle;
  uint64_t fetch_cycle;
  uint8_t prediction_confidence;

  DecodeStage()
      : pc(0), valid(false), predicted_taken(false), predicted_target(0),
        decode_cycle(0), fetch_cycle(0), prediction_confidence(1) {}
};

struct ExecuteStage {
  DecodedInstruction instr;
  uint64_t pc;
  bool valid;
  bool predicted_taken;
  uint64_t predicted_target;
  uint64_t execute_cycle;
  uint64_t fetch_cycle;
  uint8_t prediction_confidence;
  ExecutionUnit::Type unit_type;
  uint32_t execution_latency; // Cycles this instruction will occupy the unit

  ExecuteStage()
      : pc(0), valid(false), predicted_taken(false), predicted_target(0),
        execute_cycle(0), fetch_cycle(0), prediction_confidence(1),
        unit_type(ExecutionUnit::ALU), execution_latency(1) {}
};

struct MemoryStage {
  DecodedInstruction instr;
  uint64_t pc;
  bool valid;
  uint64_t memory_cycle;
  uint64_t fetch_cycle;

  MemoryStage() : pc(0), valid(false), memory_cycle(0), fetch_cycle(0) {}
};

struct WriteBackStage {
  DecodedInstruction instr;
  uint64_t pc;
  bool valid;
  uint64_t writeback_cycle;
  uint64_t fetch_cycle;

  WriteBackStage() : pc(0), valid(false), writeback_cycle(0), fetch_cycle(0) {}
};

// Reorder Buffer Entry for Out-of-Order Execution
struct ROBEntry {
  uint64_t pc;
  DecodedInstruction instr;
  bool valid;
  bool completed;
  bool exception;
  uint32_t exception_code;
  uint64_t issue_cycle;
  uint64_t complete_cycle;
  uint32_t dest_register;
  uint64_t dest_value;
  bool dest_valid;
  uint32_t rob_index;

  // For precise exception handling
  bool speculative;
  bool branch_mispredicted;
  uint64_t predicted_target;

  ROBEntry()
      : pc(0), valid(false), completed(false), exception(false),
        exception_code(0), issue_cycle(0), complete_cycle(0), dest_register(0),
        dest_value(0), dest_valid(false), rob_index(0), speculative(false),
        branch_mispredicted(false), predicted_target(0) {}
};

// Reorder Buffer for Out-of-Order Execution
class ReorderBuffer {
public:
  static constexpr size_t ROB_SIZE = 128; // Typical ROB size

  ReorderBuffer();
  ~ReorderBuffer() = default;

  // ROB management
  uint32_t AllocateEntry(uint64_t pc, const DecodedInstruction &instr);
  void CompleteEntry(uint32_t rob_index, uint64_t result_value = 0);
  void MarkException(uint32_t rob_index, uint32_t exception_code);
  bool CanCommit() const;
  ROBEntry *GetCommitEntry();
  void CommitEntry();
  void FlushAfter(uint32_t rob_index);
  void FlushAll();

  // Query methods
  bool IsFull() const;
  bool IsEmpty() const;
  size_t GetSize() const;
  ROBEntry *GetEntry(uint32_t rob_index);
  const ROBEntry *GetEntry(uint32_t rob_index) const;

  // Statistics
  uint64_t GetCommittedInstructions() const { return committed_instructions; }
  uint64_t GetFlushedInstructions() const { return flushed_instructions; }

private:
  std::array<ROBEntry, ROB_SIZE> entries;
  uint32_t head; // Points to oldest entry (commit point)
  uint32_t tail; // Points to next free entry
  size_t count;  // Number of valid entries

  // Statistics
  uint64_t committed_instructions;
  uint64_t flushed_instructions;

  // Helper methods
  uint32_t NextIndex(uint32_t index) const;
  bool IsValidIndex(uint32_t index) const;
};

// Reservation Station Entry for Out-of-Order Execution
struct ReservationStationEntry {
  bool valid;
  bool ready;
  uint32_t rob_index;
  DecodedInstruction instr;
  uint64_t pc;

  // Operand tracking
  struct Operand {
    bool ready;
    uint64_t value;
    uint32_t rob_tag; // ROB index producing this value

    Operand() : ready(false), value(0), rob_tag(0) {}
  };

  Operand operand1;
  Operand operand2;
  Operand operand3;

  // Execution tracking
  uint64_t issue_cycle;
  uint32_t execution_latency;
  ExecutionUnit::Type unit_type;

  ReservationStationEntry()
      : valid(false), ready(false), rob_index(0), pc(0), issue_cycle(0),
        execution_latency(1), unit_type(ExecutionUnit::ALU) {}
};

// Reservation Station for specific execution unit types
class ReservationStation {
public:
  static constexpr size_t RS_SIZE = 16; // Entries per reservation station

  ReservationStation(ExecutionUnit::Type type);
  ~ReservationStation() = default;

  // Entry management
  uint32_t AllocateEntry(uint32_t rob_index, const DecodedInstruction &instr,
                         uint64_t pc);
  void ReleaseEntry(uint32_t rs_index);
  bool HasReadyEntry() const;
  uint32_t GetReadyEntry() const;
  void MarkReady(uint32_t rs_index);

  // Operand management
  void SetOperand(uint32_t rs_index, int operand_num, uint64_t value,
                  bool ready = true);
  void UpdateOperandFromROB(uint32_t rob_tag, uint64_t value);

  // Query methods
  bool IsFull() const;
  bool IsEmpty() const;
  size_t GetSize() const;
  ExecutionUnit::Type GetType() const { return unit_type; }
  ReservationStationEntry *GetEntry(uint32_t rs_index);
  const ReservationStationEntry *GetEntry(uint32_t rs_index) const;

private:
  std::array<ReservationStationEntry, RS_SIZE> entries;
  ExecutionUnit::Type unit_type;
  size_t count;

  // Helper methods
  bool IsValidIndex(uint32_t index) const;
};

// Register Alias Table for Register Renaming
class RegisterAliasTable {
public:
  static constexpr size_t NUM_ARCH_REGISTERS =
      32; // x86-64 architectural registers
  static constexpr size_t NUM_PHYS_REGISTERS =
      128; // Physical register file size

  RegisterAliasTable();
  ~RegisterAliasTable() = default;

  // Register renaming operations
  uint32_t AllocatePhysicalRegister();
  void ReleasePhysicalRegister(uint32_t phys_reg);
  uint32_t GetPhysicalRegister(uint32_t arch_reg) const;
  void MapRegister(uint32_t arch_reg, uint32_t phys_reg);
  void CommitMapping(uint32_t arch_reg);

  // Checkpoint and recovery for branch misprediction
  struct Checkpoint {
    std::array<uint32_t, NUM_ARCH_REGISTERS> mapping;
    std::array<bool, NUM_PHYS_REGISTERS> free_list;
    uint32_t free_list_head;
    uint32_t free_list_tail;
    size_t free_count;
  };

  uint32_t CreateCheckpoint();
  void RestoreCheckpoint(uint32_t checkpoint_id);
  void ReleaseCheckpoint(uint32_t checkpoint_id);

  // Query methods
  bool HasFreeRegisters() const;
  size_t GetFreeRegisterCount() const;
  bool IsPhysicalRegisterFree(uint32_t phys_reg) const;

  // Statistics
  uint64_t GetRenameCount() const { return rename_count; }
  uint64_t GetRecoveryCount() const { return recovery_count; }

private:
  // Architectural to physical register mapping
  std::array<uint32_t, NUM_ARCH_REGISTERS> arch_to_phys;

  // Free list of physical registers (circular buffer)
  std::array<uint32_t, NUM_PHYS_REGISTERS> free_list;
  uint32_t free_list_head;
  uint32_t free_list_tail;
  size_t free_count;

  // Checkpoints for recovery
  std::unordered_map<uint32_t, Checkpoint> checkpoints;
  uint32_t next_checkpoint_id;

  // Statistics
  uint64_t rename_count;
  uint64_t recovery_count;

  // Helper methods
  uint32_t NextFreeListIndex(uint32_t index) const;
  void InitializeFreeList();
};

// Pipeline statistics structure
struct PipelineStats {
  uint64_t cycles;
  uint64_t instructionsExecuted;
  uint64_t stalls;
  uint64_t data_hazard_stalls;
  uint64_t structural_hazard_stalls;
  uint64_t
      memory_stalls; // Not explicitly tracked in current impl, but good to have
  uint64_t branch_hits;
  uint64_t branch_mispredictions;
  uint64_t avg_instruction_latency; // Calculated average
  uint64_t simd_instructions;       // Count of SIMD instructions executed
  uint64_t fetch_cycle_start;       // Not used as a counter, but as a timestamp
  uint64_t jit_executions;
  uint64_t jit_fallbacks;
  uint64_t jit_compile_failures;
  uint64_t jit_cache_hits;
  uint64_t jit_cache_misses;
  uint64_t memory_protection_faults;
  uint64_t tlb_hits;
  uint64_t tlb_misses;
  uint64_t cache_l1_hits;
  uint64_t cache_l1_misses;
  uint64_t cache_l2_hits;
  uint64_t cache_l2_misses;
  uint64_t prefetch_hits;
  uint64_t prefetch_misses;
  uint64_t thread_switches;
  uint64_t lock_contentions;  // Number of times a mutex acquisition failed on
                              // first try
  uint64_t atomic_operations; // Count of atomic operations (if tracked)
  double ipc;                 // Instructions per cycle (calculated)
  double branch_prediction_accuracy; // Calculated
  double jit_efficiency;             // Calculated
  double cache_hit_ratio;            // Calculated

  PipelineStats()
      : cycles(0), instructionsExecuted(0), stalls(0), data_hazard_stalls(0),
        structural_hazard_stalls(0), memory_stalls(0), branch_hits(0),
        branch_mispredictions(0), avg_instruction_latency(0),
        simd_instructions(0), fetch_cycle_start(0), jit_executions(0),
        jit_fallbacks(0), jit_compile_failures(0), jit_cache_hits(0),
        jit_cache_misses(0), memory_protection_faults(0), tlb_hits(0),
        tlb_misses(0), cache_l1_hits(0), cache_l1_misses(0), cache_l2_hits(0),
        cache_l2_misses(0), prefetch_hits(0), prefetch_misses(0),
        thread_switches(0), lock_contentions(0), atomic_operations(0), ipc(0.0),
        branch_prediction_accuracy(0.0), jit_efficiency(0.0),
        cache_hit_ratio(0.0) {}
};

// Profiling data structure
struct ProfilingEntry {
  uint64_t cycle;
  uint64_t instructions;
  uint64_t stalls;
  std::chrono::steady_clock::time_point timestamp;

  ProfilingEntry() : cycle(0), instructions(0), stalls(0) {}
};

// Performance metrics structure
struct PerformanceMetrics {
  uint64_t total_cycles = 0;
  uint64_t total_instructions = 0;
  double avg_ipc = 0.0;
  double peak_ipc = 0.0;
  double cache_miss_rate = 0.0;
  double branch_miss_rate = 0.0;
  double pipeline_utilization = 0.0;
  const OptimizationHints *hints =
      nullptr; // Reference to pipeline configuration
};

// Main pipeline class
class Pipeline {
public:
  explicit Pipeline(X86_64CPU &cpu_ref);
  ~Pipeline() = default;

  // Core pipeline operations
  void Step();
  void Flush();
  void FlushFromStage(int stage_num);
  void ResetStats();

  // Pipeline stage methods
  void FetchMultiple();
  void Decode();
  void Execute();
  void Memory();
  void WriteBack();

  // Instruction interpretation
  void InterpretInstruction(ExecuteStage &stage);

  // Helper methods
  bool IsBranchInstruction(const DecodedInstruction &instr) const;
  bool IsSIMDInstruction(const DecodedInstruction &instr) const;
  // Removed PredictBranchTarget as its logic is integrated into BranchPredictor
  // and FetchMultiple.

  // Enhanced hazard detection
  HazardInfo DetectDataHazard(const DecodedInstruction &instr,
                              uint64_t pc) const;
  HazardInfo DetectStructuralHazard(const DecodedInstruction &instr) const;
  bool HasDataHazard(const DecodedInstruction &instr, uint64_t pc) const;
  bool HasStructuralHazard() const;
  bool Conflicts(const DecodedInstruction &a, uint64_t pc_a,
                 const DecodedInstruction &b, uint64_t pc_b) const;

  // Advanced hazard analysis
  bool HasRAWHazard(const DecodedInstruction &instr, uint64_t pc) const;
  bool HasWARHazard(const DecodedInstruction &instr, uint64_t pc) const;
  bool HasWAWHazard(const DecodedInstruction &instr, uint64_t pc) const;
  ExecutionUnit::Type GetRequiredExecutionUnit(const DecodedInstruction &instr,
                                               uint64_t pc = 0) const;
  uint32_t GetInstructionLatency(const DecodedInstruction &instr) const;

  // Branch prediction integration
  void HandleBranchMisprediction(uint64_t actual_target,
                                 uint64_t predicted_target, uint64_t branch_pc);
  void RecoverFromMisprediction(uint64_t correct_pc);

  // Validation and error handling
  bool ValidatePipelineState() const;
  void UpdateProfiling();

  // Opcode dispatch table initialization
  void InitializeOpcodeDispatchTable();

  // Fallback handler for unhandled instructions
  void HandleFallbackInstruction(ExecuteStage &exec);

  // Statistics and diagnostics
  const PipelineStats &GetStats() const { return stats; }
  std::unordered_map<std::string, uint64_t> GetDiagnostics() const;

  // Fiber management
  bool SwitchToFiber(uint64_t fiberId);

  // Out-of-order execution management
  void EnableOutOfOrderExecution(bool enable);
  bool IsOutOfOrderEnabled() const;
  void CommitInstructions();
  void HandleBranchMispredictionROB(uint64_t correct_pc);

  // Reservation Station management
  void InitializeReservationStations();
  uint32_t IssueToReservationStation(uint32_t rob_index,
                                     const DecodedInstruction &instr,
                                     uint64_t pc);
  void DispatchReadyInstructions();
  ReservationStation *GetReservationStation(ExecutionUnit::Type type);

  // Register renaming management
  uint32_t RenameRegister(uint32_t arch_reg);
  void CommitRegisterRename(uint32_t arch_reg);
  uint32_t CreateRenameCheckpoint();
  void RestoreRenameCheckpoint(uint32_t checkpoint_id);

  // Getters for stage occupancy
  size_t GetFetchStageOccupancy() const { return fetchStage.size(); }
  size_t GetDecodeStageOccupancy() const { return decodeStage.size(); }
  size_t GetExecuteStageOccupancy() const { return executeStage.size(); }
  size_t GetMemoryStageOccupancy() const { return memoryStage.size(); }
  size_t GetWriteBackStageOccupancy() const { return writeBackStage.size(); }
  size_t GetROBOccupancy() const;

private:
  // CPU reference and core components
  X86_64CPU &cpu;
  InstructionDecoder decoder;
  std::unique_ptr<X86_64JITCompiler> jit;
  BranchPredictor branchPredictor;

  // Pipeline stages
  std::vector<FetchStage> fetchStage;
  std::vector<DecodeStage> decodeStage;
  std::vector<ExecuteStage> executeStage;
  std::vector<MemoryStage> memoryStage;
  std::vector<WriteBackStage> writeBackStage;

  // Execution units for structural hazard detection
  std::vector<ExecutionUnit> execution_units;
  std::unordered_map<ExecutionUnit::Type, std::vector<size_t>> unit_map;

  // Out-of-order execution components
  ReorderBuffer reorderBuffer;
  OptimizationHints optimizationHints;
  RegisterAliasTable registerAliasTable;

  // Reservation Stations for different execution unit types
  std::unique_ptr<ReservationStation> aluReservationStation;
  std::unique_ptr<ReservationStation> fpuReservationStation;
  std::unique_ptr<ReservationStation> loadStoreReservationStation;
  std::unique_ptr<ReservationStation> branchReservationStation;

  // Pipeline configuration
  size_t
      execution_units_count; // Number of execution units (total, not per type)

  // Statistics and profiling
  PipelineStats stats;
  std::vector<ProfilingEntry> profilingData;

  // Thread safety
  mutable std::timed_mutex mutex;

  // Opcode dispatch table for efficient instruction execution
  using InstructionHandler = std::function<void(ExecuteStage &)>;
  std::unordered_map<x86_64::InstructionType, InstructionHandler>
      opcodeDispatchTable;

  // IMPROVEMENT: Add per-core contention tracking and backoff
  mutable std::atomic<uint32_t>
      contentionCounter; // Tracks consecutive contention events
  // mutable std::chrono::steady_clock::time_point lastContentionTime; // Not
  // directly used in current backoff logic

  // Helper methods
  bool TryAcquireMutexWithBackoff(
      std::unique_lock<std::timed_mutex> &lock,
      std::chrono::microseconds baseTimeout = std::chrono::microseconds(100));

  // Resource management
  void InitializeExecutionUnits();
  ExecutionUnit *AllocateExecutionUnit(ExecutionUnit::Type type);
  void ReleaseExecutionUnit(ExecutionUnit *unit);
  bool IsExecutionUnitAvailable(ExecutionUnit::Type type) const;

  // Enhanced register dependency tracking
  struct RegisterDependency {
    uint64_t write_cycle;
    uint64_t write_pc;
    uint32_t write_stage;
    bool pending_write;
    std::vector<uint64_t> read_cycles;
    std::vector<uint64_t> read_pcs;
    uint32_t last_read_stage;

    RegisterDependency()
        : write_cycle(0), write_pc(0), write_stage(0), pending_write(false),
          last_read_stage(0) {}
  };

  struct InstructionDependency {
    uint64_t pc;
    uint64_t cycle;
    uint32_t stage;
    std::vector<uint32_t> reads_from; // Register IDs this instruction reads
    std::vector<uint32_t> writes_to;  // Register IDs this instruction writes
    std::vector<uint64_t> depends_on; // PCs of instructions this depends on
    bool ready_to_execute;
    uint32_t execution_latency;

    InstructionDependency()
        : pc(0), cycle(0), stage(0), ready_to_execute(false),
          execution_latency(1) {}
  };

  // Register dependency tracking maps
  std::unordered_map<uint32_t, RegisterDependency> register_dependencies;
  std::unordered_map<uint64_t, InstructionDependency> instruction_dependencies;

  // Scoreboard for tracking register availability
  std::unordered_map<uint32_t, bool> register_scoreboard;

  // Register renaming support (for advanced out-of-order execution)
  struct PhysicalRegister {
    uint32_t physical_id;
    bool available;
    uint64_t last_write_cycle;
    uint64_t last_read_cycle;

    PhysicalRegister()
        : physical_id(0), available(true), last_write_cycle(0),
          last_read_cycle(0) {}
  };

  std::unordered_map<uint32_t, uint32_t> architectural_to_physical;
  std::vector<PhysicalRegister> physical_register_file;
  std::queue<uint32_t> free_physical_registers;

  // Dependency tracking methods
  void UpdateRegisterDependencies(const DecodedInstruction &instr,
                                  uint64_t cycle);
  void ClearOldDependencies(uint64_t current_cycle);
  void InitializeRegisterDependencyTracking();
  void UpdateInstructionDependencies(const DecodedInstruction &instr,
                                     uint64_t pc, uint64_t cycle);
  bool CheckRegisterAvailability(uint32_t reg_id) const;
  void MarkRegisterBusy(uint32_t reg_id, uint64_t pc, uint64_t cycle);
  void MarkRegisterReady(uint32_t reg_id, uint64_t cycle);
  std::vector<uint32_t>
  GetRegisterDependencies(const DecodedInstruction &instr) const;
  std::vector<uint32_t>
  GetRegisterWrites(const DecodedInstruction &instr) const;
  bool CanExecuteInstruction(uint64_t pc) const;
  void ResolveRegisterDependency(uint32_t reg_id, uint64_t cycle);

  // Register renaming methods
  uint32_t AllocatePhysicalRegister();
  void ReleasePhysicalRegister(uint32_t physical_id);
  uint32_t GetPhysicalRegister(uint32_t architectural_id);
  void MapArchitecturalRegister(uint32_t architectural_id,
                                uint32_t physical_id);
  void CommitRegisterMapping(uint32_t architectural_id);
  void FlushRegisterMappings();
};

// Exception types for pipeline operations
class PipelineFetchException : public PipelineException {
public:
  explicit PipelineFetchException(const std::string &msg, uint64_t pc)
      : PipelineException("Fetch error at 0x" +
                          (pc == 0 ? "UNKNOWN" : std::to_string(pc)) + ": " +
                          msg),
        pc_(pc) {}
  uint64_t GetPC() const { return pc_; }

private:
  uint64_t pc_;
};

class PipelineDecodeException : public PipelineException {
public:
  explicit PipelineDecodeException(const std::string &msg, uint64_t pc)
      : PipelineException("Decode error at 0x" + std::to_string(pc) + ": " +
                          msg),
        pc_(pc) {}
  uint64_t GetPC() const { return pc_; }

private:
  uint64_t pc_;
};

class PipelineExecuteException : public PipelineException {
public:
  explicit PipelineExecuteException(const std::string &msg, uint64_t pc)
      : PipelineException("Execute error at 0x" + std::to_string(pc) + ": " +
                          msg),
        pc_(pc) {}
  uint64_t GetPC() const { return pc_; }

private:
  uint64_t pc_;
};

class PipelineMemoryException : public PipelineException {
public:
  explicit PipelineMemoryException(const std::string &msg, uint64_t pc)
      : PipelineException("Memory error at 0x" + std::to_string(pc) + ": " +
                          msg),
        pc_(pc) {}
  uint64_t GetPC() const { return pc_; }

private:
  uint64_t pc_;
};

// Pipeline utility functions - declarations only
namespace PipelineUtils {
// Performance analysis utilities
double CalculateIPC(const PipelineStats &stats);
double CalculateCacheHitRatio(const PipelineStats &stats);
double CalculateBranchPredictionAccuracy(const PipelineStats &stats);
double CalculateJITEfficiency(const PipelineStats &stats);

// Pipeline configuration validation
bool ValidateConfiguration(const OptimizationHints &hints);

// Performance monitoring
PerformanceMetrics AnalyzePerformance(const std::vector<ProfilingEntry> &data,
                                      const OptimizationHints &hints);
} // namespace PipelineUtils

} // namespace x86_64
